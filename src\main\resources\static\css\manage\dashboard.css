/* 한림공원 QR 체험 - 관리자 대시보드 스타일 */

/* 대시보드 카드 */
.dashboard-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* 통계 카드 */
.stat-card {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

/* 통계 숫자 */
.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* 통계 라벨 */
.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* 통계 아이콘 */
.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* 차트 컨테이너 */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

/* 차트 헤더 */
.chart-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

/* 최근 활동 */
.recent-activity {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.activity-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.activity-icon.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.activity-icon.info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 빠른 액션 */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.quick-action-btn {
    display: block;
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    color: #2d3748;
    text-decoration: none;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    border-color: #2E8B57;
    background-color: rgba(46, 139, 87, 0.05);
    color: #2E8B57;
    text-decoration: none;
    transform: translateY(-2px);
}

.quick-action-btn:last-child {
    margin-bottom: 0;
}

.quick-action-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
    
    .chart-container,
    .recent-activity,
    .quick-actions {
        margin-bottom: 1.5rem;
    }
}
