/* 한림공원 QR 체험 - 퀴즈 카테고리 관리 스타일 */

/* 카테고리 카드 */
.category-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

/* 카테고리 헤더 */
.category-header {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

/* 카테고리 제목 */
.category-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

/* 카테고리 설명 */
.category-description {
    margin-top: 0.5rem;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* 카테고리 본문 */
.category-body {
    padding: 1.5rem;
}

/* 카테고리 통계 */
.category-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2E8B57;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 카테고리 액션 */
.category-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.category-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* 폼 컨테이너 */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

/* 폼 카드 */
.form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

/* 폼 헤더 */
.form-header {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.form-subtitle {
    margin-top: 0.5rem;
    opacity: 0.9;
}

/* 폼 본문 */
.form-body {
    padding: 2rem;
}

/* 폼 그룹 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #2E8B57;
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

/* 텍스트 에어리어 */
.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* 파일 업로드 */
.file-upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #2E8B57;
    background-color: rgba(46, 139, 87, 0.05);
}

.file-upload-icon {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.file-upload-text {
    color: #4a5568;
    font-weight: 500;
}

.file-upload-hint {
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 이미지 미리보기 */
.image-preview {
    max-width: 200px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 1rem;
}

/* 폼 액션 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

/* 검색 및 필터 */
.search-filter-bar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.search-input {
    border-radius: 25px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
}

.search-input:focus {
    border-color: #2E8B57;
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .category-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .category-actions {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-container {
        margin: 0 1rem;
    }
}

/* 카테고리 이름 */
.category-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2E8B57;
}

/* 카테고리 설명 */
.category-description {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 액션 버튼 */
.btn-action {
    margin: 0 2px;
}

/* 빈 상태 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 그라데이션 배경 */
.bg-gradient-primary {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
}

/* 제출 버튼 */
.btn-submit {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
}

/* 취소 버튼 */
.btn-cancel {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

/* 카테고리 미리보기 */
.category-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

/* 필수 표시 */
.required {
    color: #dc3545;
}
