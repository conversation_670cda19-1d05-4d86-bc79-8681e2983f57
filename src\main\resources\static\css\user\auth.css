/* 한림공원 QR 체험 - 사용자 인증 페이지 스타일 */

/* 로그인 컨테이너 */
.login-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

/* 로그인 카드 */
.login-card {
    max-width: 400px;
    margin: 0 auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* 로그인 헤더 */
.login-header {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
    text-align: center;
}

/* 로그인 본문 */
.login-body {
    padding: 2rem;
}

/* 폼 플로팅 */
.form-floating {
    margin-bottom: 1rem;
}

/* 로그인 아이콘 */
.login-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* 소셜 로그인 버튼 */
.social-login-btn {
    border-radius: 10px;
    padding: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.social-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 구글 로그인 */
.google-login {
    background-color: #db4437;
    border-color: #db4437;
    color: white;
}

.google-login:hover {
    background-color: #c23321;
    border-color: #c23321;
}

/* 카카오 로그인 */
.kakao-login {
    background-color: #fee500;
    border-color: #fee500;
    color: #3c1e1e;
}

.kakao-login:hover {
    background-color: #fdd835;
    border-color: #fdd835;
}

/* 네이버 로그인 */
.naver-login {
    background-color: #03c75a;
    border-color: #03c75a;
    color: white;
}

.naver-login:hover {
    background-color: #02b351;
    border-color: #02b351;
}

/* 로그인 버튼 */
.btn-login {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 139, 87, 0.3);
}

/* 기억하기 체크박스 */
.remember-me {
    margin: 1rem 0;
}

/* 로그인 푸터 */
.login-footer {
    text-align: center;
    padding: 1rem 2rem 2rem;
    border-top: 1px solid #eee;
}

/* 로그인 아이콘 (헤더용) */
.login-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}
